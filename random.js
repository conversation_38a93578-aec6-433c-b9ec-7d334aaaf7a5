// ==UserScript==
// @name         Auto HDR Ultra Optimized
// @namespace    http://taeparlaytampermonkey.net/
// @version      2.1
// @description  Ultra-optimized HDR effect with minimal overhead, smart caching, and efficient processing.
// <AUTHOR>
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    const SCRIPT_NAME = 'AutoHDRSettings';

    // Ultra-efficient caching system
    const processedElements = new WeakSet();
    const crossOriginCache = new Map();
    let intersectionObserver = null;
    let mutationObserver = null;

    // Pre-compiled filter strings for performance
    let cssFilterString = '';
    let siteExclusionCache = null;
    let lastHref = '';

    // --- Default Settings ---
    const DEFAULT_SETTINGS = {
        hdrEnabled: true,
        brightness: 1.00,
        contrast: 1.05,
        saturation: 1.10,
        highlightReduction: 0.45,
        highlightThreshold: 230,
        excludedSites: ["example.com/disable"],
        maxCanvasDimension: 2000,
        processSVGs: false,
        enableGUISettings: true,
        lazyProcessing: true, // New: Enable intersection observer for better performance
        processOnlyVisible: true, // New: Only process visible images
    };

    let settings = { ...DEFAULT_SETTINGS };

    // --- Settings Management (Optimized) ---
    function loadSettings() {
        const saved = GM_getValue(SCRIPT_NAME, null);
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                settings = { ...DEFAULT_SETTINGS, ...parsed };
            } catch (e) {
                console.error("AutoHDR: Error parsing saved settings, using defaults.", e);
                settings = { ...DEFAULT_SETTINGS };
            }
        } else {
            settings = { ...DEFAULT_SETTINGS };
        }

        // Optimized type validation with single pass
        validateAndNormalizeSettings();
    }

    function validateAndNormalizeSettings() {
        // Batch validation for better performance
        const numericFields = {
            brightness: parseFloat,
            contrast: parseFloat,
            saturation: parseFloat,
            highlightReduction: parseFloat,
            highlightThreshold: (val) => parseInt(val, 10),
            maxCanvasDimension: (val) => parseInt(val, 10)
        };

        for (const [field, parser] of Object.entries(numericFields)) {
            const parsed = parser(settings[field]);
            settings[field] = isNaN(parsed) ? DEFAULT_SETTINGS[field] : parsed;
        }

        // Boolean validation
        settings.hdrEnabled = Boolean(settings.hdrEnabled);
        settings.processSVGs = Boolean(settings.processSVGs);
        settings.enableGUISettings = Boolean(settings.enableGUISettings);
        settings.lazyProcessing = Boolean(settings.lazyProcessing);
        settings.processOnlyVisible = Boolean(settings.processOnlyVisible);

        // Array validation
        if (!Array.isArray(settings.excludedSites)) {
            settings.excludedSites = DEFAULT_SETTINGS.excludedSites;
        }
    }

    function saveSettings() {
        GM_setValue(SCRIPT_NAME, JSON.stringify(settings));
        window.dispatchEvent(new CustomEvent('autoHDRSettingsChanged'));
    }

    // --- Helper Functions (Ultra-Optimized) ---
    function isCrossOrigin(img) {
        const src = img.src;
        if (crossOriginCache.has(src)) {
            return crossOriginCache.get(src);
        }

        try {
            if (src.startsWith('data:')) {
                crossOriginCache.set(src, false);
                return false;
            }
            const srcUrl = new URL(src, window.location.href);
            const isCrossOrigin = srcUrl.origin !== window.location.origin;
            crossOriginCache.set(src, isCrossOrigin);
            return isCrossOrigin;
        } catch (e) {
            crossOriginCache.set(src, true);
            return true;
        }
    }

    // Ultra-optimized constants for inline processing
    const SATURATION_WEIGHTS = [0.299, 0.587, 0.114];

    // Optimized visibility check with early return
    const isElementVisible = (element) => !settings.processOnlyVisible || (
        (rect => rect.top < window.innerHeight && rect.bottom > 0 && rect.left < window.innerWidth && rect.right > 0)
        (element.getBoundingClientRect())
    );

    // --- Ultra-Optimized HDR Application ---
    function applyHDREffectToImage(img) {
        // Ultra-fast early exits
        if (img.dataset.hdrApplied || processedElements.has(img)) return;

        // Combined validation checks
        if (!img.complete || img.naturalWidth === 0 || img.naturalHeight === 0) {
            if (img.naturalWidth === 0 && img.complete) img.dataset.hdrApplied = 'invalid-dimensions';
            return;
        }

        // Lazy processing check
        if (!isElementVisible(img)) return;

        const src = img.src;

        // SVG check with early return
        if ((src.includes('.svg') || src.startsWith('data:image/svg+xml')) && !settings.processSVGs) {
            img.dataset.hdrApplied = 'skipped-svg';
            processedElements.add(img);
            return;
        }

        // Size and cross-origin checks with CSS fallback
        if (img.naturalWidth > settings.maxCanvasDimension || img.naturalHeight > settings.maxCanvasDimension || isCrossOrigin(img)) {
            // Pre-compiled CSS filter for performance
            if (!cssFilterString) {
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
            }
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = img.naturalWidth > settings.maxCanvasDimension ? 'css-only-large' : 'css-only-crossorigin';
            processedElements.add(img);
            return;
        }

        // Canvas processing for full HDR effect
        processImageWithCanvas(img);
    }

    // Ultra-optimized canvas processing
    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Pre-calculate all values for maximum performance
            const { brightness, contrast, saturation, highlightReduction, highlightThreshold } = settings;
            const needsSaturation = saturation !== 1.0;
            const needsHighlightReduction = highlightReduction > 0;
            const contrastOffset = 128;

            // Ultra-optimized pixel processing with minimal function calls
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i], g = data[i + 1], b = data[i + 2];

                // Combined contrast and brightness in single operation
                r = ((r - contrastOffset) * contrast + contrastOffset) * brightness;
                g = ((g - contrastOffset) * contrast + contrastOffset) * brightness;
                b = ((b - contrastOffset) * contrast + contrastOffset) * brightness;

                // Conditional processing with inline operations
                if (needsSaturation) {
                    const gray = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;
                    r = gray + (r - gray) * saturation;
                    g = gray + (g - gray) * saturation;
                    b = gray + (b - gray) * saturation;
                }

                if (needsHighlightReduction) {
                    const invReduction = 1 - highlightReduction;
                    if (r > highlightThreshold) r = highlightThreshold + (r - highlightThreshold) * invReduction;
                    if (g > highlightThreshold) g = highlightThreshold + (g - highlightThreshold) * invReduction;
                    if (b > highlightThreshold) b = highlightThreshold + (b - highlightThreshold) * invReduction;
                }

                // Inline clamping for maximum performance
                data[i] = r < 0 ? 0 : r > 255 ? 255 : Math.round(r);
                data[i + 1] = g < 0 ? 0 : g > 255 ? 255 : Math.round(g);
                data[i + 2] = b < 0 ? 0 : b > 255 ? 255 : Math.round(b);
            }

            ctx.putImageData(imageData, 0, 0);

            // Efficient src handling
            if (!img.dataset.originalSrc && !img.src.startsWith('data:')) img.dataset.originalSrc = img.src;
            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas-processed';
            processedElements.add(img);

        } catch (e) {
            // Fallback with pre-compiled filter
            if (!cssFilterString) cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'error-canvas css-fallback';
            processedElements.add(img);
        }
    }

    // Ultra-optimized video processing
    function applyHDRToVideos() {
        const videos = document.querySelectorAll('video:not([data-hdrApplied="video-processed"])');
        if (videos.length === 0) return;

        // Use pre-compiled filter string
        if (!cssFilterString) cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;

        videos.forEach(video => {
            if (isElementVisible(video)) {
                video.style.filter = cssFilterString;
                video.dataset.hdrApplied = 'video-processed';
                processedElements.add(video);
            }
        });
    }

    // Ultra-optimized intersection observer
    function setupIntersectionObserver() {
        if (!settings.lazyProcessing || intersectionObserver) return;

        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const tagName = element.tagName;

                    if (tagName === 'IMG' && !element.dataset.hdrApplied) {
                        applyHDREffectToImage(element);
                    } else if (tagName === 'VIDEO' && !element.dataset.hdrApplied) {
                        if (!cssFilterString) cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
                        element.style.filter = cssFilterString;
                        element.dataset.hdrApplied = 'video-processed';
                        processedElements.add(element);
                    }
                    intersectionObserver.unobserve(element);
                }
            });
        }, { rootMargin: '50px', threshold: 0.1 });
    }

    const cleanupIntersectionObserver = () => {
        if (intersectionObserver) {
            intersectionObserver.disconnect();
            intersectionObserver = null;
        }
    };

    // Ultra-optimized revert function
    const revertElement = (el) => {
        if (!el.dataset.hdrApplied) return;

        const wasCanvasProcessed = el.dataset.hdrApplied.includes('canvas-processed');
        const originalSrc = el.dataset.originalSrc;

        el.style.filter = '';

        // Efficient src restoration
        if (wasCanvasProcessed && originalSrc && el.src?.startsWith('data:image') && el.src !== originalSrc) {
            el.src = originalSrc;
        }

        // Batch attribute removal
        ['data-hdrApplied', 'data-originalSrc', 'data-hdrListener'].forEach(attr => {
            if (el.hasAttribute(attr)) el.removeAttribute(attr);
        });

        processedElements.delete(el);
    };


    // Ultra-optimized media processing
    function processAllMedia() {
        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        // Lazy processing setup
        if (settings.lazyProcessing) setupIntersectionObserver();

        // Efficient image processing
        const images = document.querySelectorAll('img:not([data-hdrApplied])');
        images.forEach(img => {
            if (settings.lazyProcessing && intersectionObserver) {
                intersectionObserver.observe(img);
            } else if (img.complete) {
                applyHDREffectToImage(img);
            } else if (!img.dataset.hdrListener) {
                // Inline load listener for efficiency
                const onLoad = () => {
                    applyHDREffectToImage(img);
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                const onError = () => {
                    img.dataset.hdrApplied = 'error-load';
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
                img.dataset.hdrListener = 'true';
            }
        });

        applyHDRToVideos();
    }

    // --- Site Exclusion & Debounce (Ultra-Optimized) ---
    function isSiteExcluded() {
        const currentHref = window.location.href;

        // Use cache if URL hasn't changed
        if (currentHref === lastHref && siteExclusionCache !== null) {
            return siteExclusionCache;
        }

        lastHref = currentHref;

        if (!Array.isArray(settings.excludedSites) || settings.excludedSites.length === 0) {
            siteExclusionCache = false;
            return false;
        }

        siteExclusionCache = settings.excludedSites.some(site =>
            site && typeof site === 'string' && site.trim() !== '' && currentHref.includes(site.trim())
        );

        return siteExclusionCache;
    }

    // Ultra-optimized debounce
    const debounce = (fn, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    };

    // Optimized for responsiveness
    const debouncedProcessMedia = debounce(processAllMedia, 100);

    // Ultra-optimized mutation observer
    function startObserver() {
        if (mutationObserver) mutationObserver.disconnect();

        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        mutationObserver = new MutationObserver((mutationsList) => {
            let needsProcessing = false;

            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Ultra-efficient media element detection
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === 1) { // ELEMENT_NODE
                            const tagName = node.tagName;
                            if (tagName === 'IMG' || tagName === 'VIDEO' ||
                                (node.querySelector && node.querySelector('img, video'))) {
                                needsProcessing = true;
                                break;
                            }
                        }
                    }
                } else if (mutation.type === 'attributes') {
                    const target = mutation.target;
                    const tagName = target.tagName;

                    if ((tagName === 'IMG' || tagName === 'VIDEO') && target.dataset.hdrApplied) {
                        if ((mutation.attributeName === 'src' && !target.src.startsWith('data:image')) ||
                            (mutation.attributeName === 'style' && !target.style.filter?.includes('brightness'))) {
                            revertElement(target);
                            needsProcessing = true;
                        }
                    }
                }

                if (needsProcessing) break;
            }

            if (needsProcessing) debouncedProcessMedia();
        });

        const target = document.body || document.documentElement;
        if (target) {
            mutationObserver.observe(target, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['src', 'style']
            });
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                mutationObserver.observe(document.body || document.documentElement, {
                    childList: true, subtree: true, attributes: true, attributeFilter: ['src', 'style']
                });
            }, { once: true });
        }

        debouncedProcessMedia();
    }


    function init() {
        loadSettings();
        // console.log("AutoHDR Initialized. Settings:", JSON.parse(JSON.stringify(settings))); // Deep copy for logging

        if (settings.enableGUISettings) {
            // Wait for body to ensure GUI can be appended
            if (document.body) {
                createSettingsGUI();
            } else {
                document.addEventListener('DOMContentLoaded', createSettingsGUI, { once: true });
            }
        }

        startObserver();

        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                // console.log("AutoHDR: Settings changed via localStorage.");
                const oldEnabled = settings.hdrEnabled;
                const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                loadSettings();

                const newExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                // If enabled status changed, or excluded sites changed, or if it's still enabled (other settings might have changed)
                if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                    // console.log("AutoHDR: Re-evaluating observer and media due to settings change from storage event.");
                    startObserver(); // This will disconnect, revert if needed, and then restart/reprocess.
                }
            }
        });

        window.addEventListener('autoHDRSettingsChanged', () => {
            // console.log("AutoHDR: Settings changed via custom event (save from GUI).");
            const oldEnabled = settings.hdrEnabled;
            const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);
            // loadSettings(); // Settings are already updated by GUI before saveSettings() -> event.
                              // But if saveSettings() was called externally without updating 'settings' var first, then load would be needed.
                              // For now, GUI ensures 'settings' is fresh.

            const newExcludedSitesJSON = JSON.stringify(settings.excludedSites); // settings should be fresh from GUI update.

            if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                // console.log("AutoHDR: Re-evaluating observer and media due to settings change from custom event.");
                startObserver();
            }
        });

        if (document.readyState === 'complete') {
            debouncedProcessMedia();
        } else {
            window.addEventListener('load', debouncedProcessMedia, { once: true });
        }
    }

// --- Settings GUI (Sederhana) ---
    function createSettingsGUI() {
        if (document.getElementById('autohdr-settings-button') || !document.body) return;

        GM_addStyle(`
            /* Minimalist HDR Settings UI */
            #autohdr-settings-button {
                position: fixed;
                top: 50%;
                left: -25px;
                z-index: 2147483646;
                background: rgba(0, 0, 0, 0.8);
                color: #fff;
                border: none;
                padding: 8px 6px;
                border-radius: 0 4px 4px 0;
                cursor: pointer;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 10px;
                font-weight: 500;
                opacity: 0.6;
                transition: all 0.2s ease;
                user-select: none;
                backdrop-filter: blur(8px);
                transform: translateY(-50%);
                min-width: 30px;
                text-align: center;
                letter-spacing: 0.5px;
            }
            #autohdr-settings-button:hover {
                left: 0px;
                opacity: 1;
                background: rgba(0, 0, 0, 0.9);
                transform: translateY(-50%) scale(1.05);
            }
            #autohdr-settings-panel {
                position: fixed;
                top: 50%;
                left: -280px;
                z-index: 2147483645;
                background: rgba(255, 255, 255, 0.95);
                color: #333;
                border: none;
                border-radius: 0 8px 8px 0;
                padding: 20px;
                display: none;
                flex-direction: column;
                gap: 12px;
                font-family: system-ui, -apple-system, sans-serif;
                font-size: 12px;
                width: 260px;
                max-height: 70vh;
                overflow-y: auto;
                box-shadow: 0 8px 32px rgba(0,0,0,0.15);
                backdrop-filter: blur(12px);
                transform: translateY(-50%);
                transition: left 0.25s ease-out, opacity 0.25s ease-out;
            }
            #autohdr-settings-panel.show {
                left: 0px;
                opacity: 1;
            }

            @keyframes slideInFromLeft {
                from {
                    opacity: 0;
                    left: -320px;
                    transform: translateY(-50%) translateX(-20px);
                }
                to {
                    opacity: 1;
                    left: 0px;
                    transform: translateY(-50%) translateX(0);
                }
            }
            #autohdr-settings-panel label {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-weight: 400;
            }
            #autohdr-settings-panel input[type="number"],
            #autohdr-settings-panel input[type="text"],
            #autohdr-settings-panel textarea {
                width: 70px;
                background: rgba(0, 0, 0, 0.05);
                color: #333;
                border: 1px solid rgba(0, 0, 0, 0.1);
                padding: 4px 6px;
                border-radius: 3px;
                transition: all 0.2s ease;
                font-size: 11px;
            }
            #autohdr-settings-panel input[type="number"]:focus,
            #autohdr-settings-panel input[type="text"]:focus,
            #autohdr-settings-panel textarea:focus {
                outline: none;
                border-color: rgba(0, 0, 0, 0.3);
                background: rgba(0, 0, 0, 0.08);
            }
            #autohdr-settings-panel input[type="checkbox"] {
                margin-right: 6px;
                transform: scale(1.1);
                accent-color: #333;
            }
            #autohdr-settings-panel textarea {
                width: 100%;
                min-height: 40px;
                margin-top: 4px;
                resize: vertical;
                font-family: inherit;
            }
            #autohdr-settings-panel button#autohdr-save-settings {
                padding: 8px 16px;
                background: #333;
                color: #fff;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                font-size: 11px;
                transition: all 0.2s ease;
                margin-top: 12px;
                width: 100%;
            }
            #autohdr-settings-panel button#autohdr-save-settings:hover {
                background: #000;
                transform: translateY(-1px);
            }
            #autohdr-settings-panel .autohdr-title {
                font-weight: 600;
                font-size: 14px;
                margin-bottom: 16px;
                text-align: center;
                color: #333;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                padding-bottom: 8px;
            }
            #autohdr-settings-panel .autohdr-section-title {
                font-weight: 500;
                margin-top: 12px;
                margin-bottom: 6px;
                color: #666;
                font-size: 11px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            /* Performance optimization */
            #autohdr-settings-button, #autohdr-settings-panel {
                will-change: transform, opacity;
            }
        `);

        const panel = document.createElement('div');
        panel.id = 'autohdr-settings-panel';

        // Helper function to create elements
        function createElementWithProps(tag, props, children = []) {
            const el = document.createElement(tag);
            for (const key in props) {
                if (key === 'textContent') el.textContent = props[key];
                else if (key === 'className') el.className = props[key];
                else el.setAttribute(key, props[key]);
            }
            children.forEach(child => el.appendChild(child));
            return el;
        }

        // Helper function to create a label with an input
        function createLabelWithInput(labelText, inputProps, isCheckbox = false, inputTag = 'input') {
            const input = createElementWithProps(inputTag, inputProps);
            const label = createElementWithProps('label', { textContent: labelText + (isCheckbox ? '' : ': ') });
            if (isCheckbox) {
                label.insertBefore(input, label.firstChild); // Checkbox first, then text
                 // Add space after checkbox text if needed, or adjust labelText
                 label.appendChild(document.createTextNode(labelText));
                 input.style.marginRight = '5px'; // Spasi antara checkbox dan teks
                 // Hapus textContent awal agar tidak duplikat
                 label.firstChild.nextSibling.remove(); // Hapus text node yang ditambahkan oleh labelText
            } else {
                label.appendChild(input);
            }
            return label;
        }
         // Helper function to create a label with an input (checkbox style: input first)
        function createCheckboxLabel(labelText, inputProps) {
            const input = createElementWithProps('input', { type: 'checkbox', ...inputProps });
            const label = document.createElement('label');
            label.appendChild(input);
            label.appendChild(document.createTextNode(' ' + labelText)); // Tambahkan spasi
            return label;
        }


        // Panel Title
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-title', textContent: 'Auto HDR Settings' }));

        // HDR Enabled
        panel.appendChild(createCheckboxLabel('Enable HDR', { id: 'hdrEnabled' }));


        // Adjustments Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Adjustments' }));
        panel.appendChild(createLabelWithInput('Brightness', { id: 'brightness', type: 'number', step: '0.01', min: '0' }));
        panel.appendChild(createLabelWithInput('Contrast', { id: 'contrast', type: 'number', step: '0.01', min: '0' }));
        panel.appendChild(createLabelWithInput('Saturation', { id: 'saturation', type: 'number', step: '0.01', min: '0' }));

        // Highlights Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Highlights' }));
        panel.appendChild(createLabelWithInput('Reduction', { id: 'highlightReduction', type: 'number', step: '0.01', min: '0', max: '1' }));
        panel.appendChild(createLabelWithInput('Threshold', { id: 'highlightThreshold', type: 'number', step: '1', min: '0', max: '255' }));

        // Performance & Misc Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Performance & Misc' }));
        panel.appendChild(createLabelWithInput('Max Canvas Dim (px)', { id: 'maxCanvasDimension', type: 'number', step: '100', min: '200' }));
        panel.appendChild(createCheckboxLabel('Process SVGs (Canvas)', { id: 'processSVGs' }));
        panel.appendChild(createCheckboxLabel('Lazy Processing (Viewport)', { id: 'lazyProcessing' }));
        panel.appendChild(createCheckboxLabel('Process Only Visible', { id: 'processOnlyVisible' }));

        // Excluded Sites
        const excludedSitesLabel = createLabelWithInput('Excluded Sites (CSV)',
            { id: 'excludedSites', title: "Comma-separated list of partial URLs to exclude. E.g., 'google.com, anothersite.net/path'" },
            false,
            'textarea'
        );
        panel.appendChild(excludedSitesLabel);


        // Save Button
        panel.appendChild(createElementWithProps('button', { id: 'autohdr-save-settings', textContent: 'Save & Apply' }));

        document.body.appendChild(panel);

        // Populate GUI with current settings (optimized)
        function populateGUISettings() {
            const elements = {
                hdrEnabled: settings.hdrEnabled,
                brightness: settings.brightness.toFixed(2),
                contrast: settings.contrast.toFixed(2),
                saturation: settings.saturation.toFixed(2),
                highlightReduction: settings.highlightReduction.toFixed(2),
                highlightThreshold: settings.highlightThreshold,
                maxCanvasDimension: settings.maxCanvasDimension,
                processSVGs: settings.processSVGs,
                lazyProcessing: settings.lazyProcessing,
                processOnlyVisible: settings.processOnlyVisible,
                excludedSites: settings.excludedSites.join(', ')
            };

            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = value;
                    } else {
                        element.value = value;
                    }
                }
            }
        }

        populateGUISettings();


        const button = document.createElement('button');
        button.id = 'autohdr-settings-button';
        button.textContent = 'HDR';

        // Hover functionality for floating button
        let hoverTimeout;

        button.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            const panelElement = document.getElementById('autohdr-settings-panel');
            panelElement.style.display = 'flex';
            setTimeout(() => panelElement.classList.add('show'), 10);
            populateGUISettings();
        });

        button.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                const panelElement = document.getElementById('autohdr-settings-panel');
                if (!panelElement.matches(':hover')) {
                    panelElement.classList.remove('show');
                    setTimeout(() => panelElement.style.display = 'none', 300);
                }
            }, 300);
        });

        // Click functionality as backup
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const panelElement = document.getElementById('autohdr-settings-panel');
            const isVisible = panelElement.classList.contains('show');

            if (isVisible) {
                panelElement.classList.remove('show');
                setTimeout(() => panelElement.style.display = 'none', 300);
            } else {
                panelElement.style.display = 'flex';
                setTimeout(() => panelElement.classList.add('show'), 10);
                populateGUISettings();
            }
        });

        document.body.appendChild(button);

        // Add hover functionality to panel
        panel.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
        });

        panel.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                panel.classList.remove('show');
                setTimeout(() => panel.style.display = 'none', 300);
            }, 300);
        });

        document.getElementById('autohdr-save-settings').addEventListener('click', () => {
            // Optimized settings update with validation
            const updates = {
                hdrEnabled: document.getElementById('hdrEnabled').checked,
                brightness: Math.max(0, parseFloat(document.getElementById('brightness').value) || DEFAULT_SETTINGS.brightness),
                contrast: Math.max(0, parseFloat(document.getElementById('contrast').value) || DEFAULT_SETTINGS.contrast),
                saturation: Math.max(0, parseFloat(document.getElementById('saturation').value) || DEFAULT_SETTINGS.saturation),
                highlightReduction: Math.max(0, Math.min(1, parseFloat(document.getElementById('highlightReduction').value) || DEFAULT_SETTINGS.highlightReduction)),
                highlightThreshold: Math.max(0, Math.min(255, parseInt(document.getElementById('highlightThreshold').value, 10) || DEFAULT_SETTINGS.highlightThreshold)),
                maxCanvasDimension: Math.max(200, parseInt(document.getElementById('maxCanvasDimension').value, 10) || DEFAULT_SETTINGS.maxCanvasDimension),
                processSVGs: document.getElementById('processSVGs').checked,
                lazyProcessing: document.getElementById('lazyProcessing').checked,
                processOnlyVisible: document.getElementById('processOnlyVisible').checked
            };

            // Update excluded sites
            const excludedText = document.getElementById('excludedSites').value;
            updates.excludedSites = excludedText.split(',').map(s => s.trim()).filter(s => s !== '');

            // Apply all updates
            Object.assign(settings, updates);

            // Clear caches and reset filter string when settings change
            crossOriginCache.clear();
            siteExclusionCache = null;
            cssFilterString = '';

            saveSettings();
            const panelElement = document.getElementById('autohdr-settings-panel');
            panelElement.classList.remove('show');
            setTimeout(() => panelElement.style.display = 'none', 300);
        });

        document.addEventListener('click', (event) => {
            const panelElement = document.getElementById('autohdr-settings-panel');
            const buttonElement = document.getElementById('autohdr-settings-button');
            if (panelElement && buttonElement && panelElement.classList.contains('show') &&
                !panelElement.contains(event.target) && !buttonElement.contains(event.target)) {
                clearTimeout(hoverTimeout);
                panelElement.classList.remove('show');
                setTimeout(() => panelElement.style.display = 'none', 300);
            }
        });
    }

    // --- Jalankan Inisialisasi ---
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();